import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  <PERSON>dal<PERSON>ody,
  ModalFooter,
  Button,
  useDisclosure,
} from "@heroui/react";

export default function LocateOnMap(
{
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}
) {

  return (
    <>
      <Modal
        isDismissable={false}
        isKeyboardDismissDisabled={true}
        isOpen={open}
        onClose={onClose}
        placement="center"
        backdrop="blur"
        classNames={{
          backdrop: "z-[9999]",
          wrapper: "z-[10000]",
          base: "z-[10001]"
        }}
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader className="flex flex-col gap-1">Locate on Map</ModalHeader>
              <ModalBody>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam pulvinar risus non
                  risus hendrerit venenatis. Pellentesque sit amet hendrerit risus, sed porttitor
                  quam.
                </p>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam pulvinar risus non
                  risus hendrerit venenatis. Pellentesque sit amet hendrerit risus, sed porttitor
                  quam.
                </p>
               
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
