import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  <PERSON>dal<PERSON>ody,
  ModalFooter,
  Button,
  useDisclosure,
} from "@heroui/react";

export default function LocateOnMap(
{
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}
) {

  return (
    <>
      <Modal
        isDismissable={false}
        isKeyboardDismissDisabled={true}
        isOpen={open}
        onClose={onClose}
        
        className="z-[100001]"
      >
        <ModalContent className="z-[9999999999999999999999999999999]">
          {() => (
            <>
              <ModalHeader className="flex flex-col gap-1">Locat on Map</ModalHeader>
              <ModalBody>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam pulvinar risus non
                  risus hendrerit venenatis. Pellentesque sit amet hendrerit risus, sed porttitor
                  quam.
                </p>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam pulvinar risus non
                  risus hendrerit venenatis. Pellentesque sit amet hendrerit risus, sed porttitor
                  quam.
                </p>
               
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
