'use client';

import { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { Input } from '@heroui/react';
import { GPSIcon, MagniferIcon, Pen2Icon, PointMapIcon } from '../../icons';
import Map, { Marker } from 'react-map-gl/mapbox'; // ✅ consolidated import
import { useDispatch, useSelector } from 'react-redux';
import { useLazyGetCityDetailsQuery } from '@/services/citydetailsApi';
import {
  setStartLocation,
  setDestination,
  setDestinations,
  removeDestination,
  setPickedCoords,
  selectStartLocation,
  selectDestination,
  selectDestinations,
  selectPickedCoords,
} from '@/slices/locationSlice';
import { selectCountries } from '@/slices/recommendationSlice';
import { useGetLocationSuggestionsMutation } from '@/services/locationApi';
import Cookies from 'js-cookie';
import LocateOnMap from './locateOnMapModal';

export default function TripFilter() {
  const dispatch = useDispatch();
  const [getCityDetails] = useLazyGetCityDetailsQuery();

  const [isMapVisible, setIsMapVisible] = useState(false);

  // Redux state
  const startLocation = useSelector(selectStartLocation);
  const destination = useSelector(selectDestination);
  const destinations = useSelector(selectDestinations);
  const pickedCoords = useSelector(selectPickedCoords);
  const countries = useSelector(selectCountries);

  // RTK Query hook
  const [getSuggestions] = useGetLocationSuggestionsMutation();

  // 🔍 Fetch suggestions whenever `destination` changes
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!destination) return;

      try {
        const res = await getSuggestions({
          query: destination,
          limit: 10,
        }).unwrap();
        dispatch(setDestinations(res.detail.data || []));
      } catch (err) {
        dispatch(setDestinations([]));
      }
    };

    fetchSuggestions();
  }, [destination, dispatch, getSuggestions]);

  // ✅ fallback API if user blocks/denies/close popup
  const fallbackGetLocation = async () => {
    try {
      const res = await getCityDetails().unwrap();
      // console.log(res.detail.data.city_name)
      if (res?.detail?.data) {
        dispatch(
          setStartLocation(
            `${res.detail.data.city_name}, ${res.detail.data.country_name}`
          )
        );
      } else {
        dispatch(setStartLocation('Unknown Location'));
      }
    } catch (err) {
      console.error('Fallback location error:', err);
      dispatch(setStartLocation('Unknown Location'));
    }
  };

  const handleGetCurrentLocation = () => {
    if (!navigator.geolocation) {
      // Browser doesn’t support geolocation
      fallbackGetLocation();
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async position => {
        // ✅ user allowed
        const { latitude, longitude } = position.coords;
        try {
          const res = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN}`
          );
          const data = await res.json();

          if (data.features && data.features.length > 0) {
            dispatch(setStartLocation(data.features[0].place_name));
          } else {
            dispatch(setStartLocation(`${latitude}, ${longitude}`));
          }
        } catch {
          dispatch(setStartLocation(`${latitude}, ${longitude}`));
        }
      },
      async error => {
        // 🔑 make this async
        // ❌ User denied, closed popup, or other error
        if (error.code === error.PERMISSION_DENIED) {
          console.warn('User denied location → fallback API');
        } else if (error.code === error.POSITION_UNAVAILABLE) {
          console.warn('Position unavailable → fallback API');
        } else if (error.code === error.TIMEOUT) {
          console.warn('Geolocation timeout → fallback API');
        } else {
          console.warn('Unknown geolocation error:', error);
        }
        await fallbackGetLocation(); // 🔑 ensure API actually fires
      },
      { timeout: 10000 }
    );
  };

  useEffect(() => {
    handleGetCurrentLocation();
    console.log(
      JSON.stringify(Cookies.get('locationCountries')),
      'locationCountries'
    );
  }, []);

  const handleMapClick = async (e: any) => {
    const { lat, lng } = e.lngLat;

    dispatch(setPickedCoords({ lat, lng }));

    try {
      const res = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN}`
      );
      const data = await res.json();

      if (data.features && data.features.length > 0) {
        dispatch(setStartLocation(data.features[0].place_name));
      } else {
        dispatch(setStartLocation(`${lat}, ${lng}`));
      }
    } catch (err) {
      dispatch(setDestination(`${lat}, ${lng}`));
    }
  };
const [locateOnMap, setLocateOnMap] = useState(false);
  return (
    <>
      <div className="w-full min-w-lg max-w-xl rounded-2xl bg-white p-6 space-y-6">
        {/* Start Location */}
        <div>
          <label className="block text-sm text-gray mb-1">Start location</label>
          <div className="relative">
            <Input
              labelPlacement="outside"
              placeholder="Enter location"
              endContent={<Pen2Icon />}
              value={startLocation}
              onChange={e => dispatch(setStartLocation(e.target.value))}
              type="text"
              variant="bordered"
            />
          </div>
          <div className="mt-3 flex gap-3">
            <button
              type="button"
              onClick={handleGetCurrentLocation}
              className="flex items-center gap-2 font-medium rounded-md border border-primary-200 px-4 py-1.5 text-sm text-primary-200 transition"
            >
              <GPSIcon className="h-4 w-4" />
              Current Location
            </button>
            <button
              type="button"
              onClick={() => {
                setLocateOnMap(true)
                setIsMapVisible(!isMapVisible);
              }}
              className="flex items-center gap-2 font-medium rounded-md border border-primary-200 px-4 py-1.5 text-sm text-primary-200 transition"
            >
              <PointMapIcon className="h-4 w-4" />
              Locate on Map
            </button>
          </div>
          {isMapVisible && (
            <div className="mt-4 border rounded-md overflow-hidden">
              <div style={{ width: '100%', height: 400 }}>
                <Map
                  initialViewState={{
                    latitude: pickedCoords?.lat || 51.505,
                    longitude: pickedCoords?.lng || -0.09,
                    zoom: 5,
                  }}
                  dragPan
                  dragRotate
                  scrollZoom
                  touchZoomRotate
                  style={{ width: '100%', height: 400 }}
                  mapStyle="mapbox://styles/mapbox/streets-v11"
                  mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN}
                  onClick={handleMapClick}
                >
                  {pickedCoords && (
                    <Marker
                      latitude={pickedCoords.lat}
                      longitude={pickedCoords.lng}
                    >
                      <div
                        style={{
                          background: 'red',
                          borderRadius: '50%',
                          width: 20,
                          height: 20,
                          border: '2px solid white',
                        }}
                      />
                    </Marker>
                  )}
                </Map>
              </div>
            </div>
          )}
        </div>

        <div>
          <div className="mt-3 mb-3 flex gap-2 flex-wrap">
            {countries.map((loc: any) => (
              <span
                key={loc}
                role="button"
                tabIndex={0}
                className="inline-flex cursor-pointer items-center gap-1 rounded-md bg-primary-200/10 text-primary-200 px-3 py-1 text-sm"
                onClick={() => dispatch(setDestination(loc))}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    setDestination(loc);
                  }
                }}
              >
                {loc}
              </span>
            ))}
          </div>
          <label className="block text-sm text-gray mb-1">
            Travel Destination
          </label>

          <div className="relative">
            <Input
              labelPlacement="outside"
              placeholder="Enter location"
              endContent={<MagniferIcon />}
              value={destination}
              onChange={e => dispatch(setDestination(e.target.value))}
              type="text"
              variant="bordered"
            />
          </div>

          {/* Destination Tags */}
          <div className="mt-3 flex gap-2 flex-wrap">
            {destinations.map((loc: any) => (
              <span
                key={loc.destination_id}
                role="button"
                tabIndex={0}
                className="inline-flex cursor-pointer items-center gap-1 rounded-md bg-primary-200/10 text-primary-200 px-3 py-1 text-sm"
                onClick={() => dispatch(setDestination(loc.name))}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    setDestination(loc.name);
                  }
                }}
              >
                {loc.name}

                <button
                  type="button"
                  onClick={() => dispatch(removeDestination(loc.destination_id))}
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* LocateOnMap Modal - Moved outside the main container for proper z-index layering */}
      <LocateOnMap
        open={locateOnMap}
        onClose={() => setLocateOnMap(false)}
      />
    </>
  );
}
