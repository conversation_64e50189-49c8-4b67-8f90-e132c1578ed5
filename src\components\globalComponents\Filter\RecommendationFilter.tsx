'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from '@heroui/modal';
import { Checkbox } from '@heroui/checkbox';
import { Button } from '@heroui/button';
import { Select, SelectItem } from '@heroui/select';
import { Slider } from '@heroui/slider';
import { Chip } from '@heroui/chip';
import { useSelector } from 'react-redux';
import {
  addAmenity,
  applyFilters,
  removeAmenity,
  resetFilters,
  selectAmenities,
  selectCities,
  selectFilterTypes,
  selectPrice,
  selectSelectedCity,
  selectSelectedTypes,
  setCity,
  setPrice,
  toggleType,
} from '@/slices/recommendationSlice';
import { useDispatch } from 'react-redux';
import type { SharedSelection } from '@heroui/react';

const amenitiesList = [
  'Air conditioning',
  'Assisted living',
  'Disability Access',
  'Controlled access',
  'Cable Ready',
  'Available now',
  'College',
  'Corporate',
  'Elevator',
  'Extra Storage',
  'High speed internet',
  'Garage',
  'Pet allowed',
];

type RecommendationFilterProps = {
  open: boolean;
  onClose: () => void;
  onApply: (filters: any) => void;
};

export default function RecommendationFilter({
  open,
  onClose,
  onApply,
}: RecommendationFilterProps) {
  const [sortBy, setSortBy] = useState<string[]>([]);
  const [location, setLocation] = useState('');
  // const [price, setPrice] = useState<[number, number]>([0, 10000000000]);
  // const [amenities, setAmenities] = useState<string[]>([]);

  const dispatch = useDispatch();

  const cities = useSelector(selectCities);
  const filterTypes = useSelector(selectFilterTypes);
  const selectedTypes = useSelector(selectSelectedTypes);
  const selectedCity = useSelector(selectSelectedCity);
  const price = useSelector(selectPrice);
  const amenities = useSelector(selectAmenities);

  // const handleApply = () => {
  //   onApply({ sortBy, location, price, amenities });
  //   onClose();
  // };

  // Handle filter type change
  const handleTypeChange = (type: string) => {
    dispatch(toggleType(type));
  };

  // Handle city change
  const handleCityChange = (keys: SharedSelection) => {
    const raw = Array.from(keys)[0] ?? '';
    const value = raw.toString();
    dispatch(setCity(value || null));
  };

  // Handle price range change
  const handlePriceChange = (val: number | number[]) => {
    if (Array.isArray(val) && val.length === 2) {
      dispatch(setPrice([val[0], val[1]]));
    }
  };

  // toggle amenities
  const toggleAmenity = (amenity: string) => {
    if (amenities.includes(amenity)) {
      dispatch(removeAmenity(amenity));
    } else {
      dispatch(addAmenity(amenity));
    }
  };

  // Apply the filters
  const applyFilterData = () => {
    dispatch(applyFilters());
    onClose();
  };

  // Reset al filters
  const resetFilterData = () => {
    dispatch(resetFilters());
  };

  return (
    <Modal isOpen={open} onClose={onClose} size="lg" scrollBehavior="inside">
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="text-default-1000">
              Recommendation Filter
            </ModalHeader>
            <ModalBody className="space-y-4">
              {/* Sort By */}
              <div>
                <p className="font-semibold mb-2 text-default-Secondary">
                  Sort by
                </p>
                <div className="flex flex-col gap-2">
                  {filterTypes.map(label => (
                    <Checkbox
                      key={label}
                      isSelected={selectedTypes.includes(label)}
                      onChange={e => handleTypeChange(label)}
                      className="text-default-1000"
                    >
                      {label}
                    </Checkbox>
                  ))}
                </div>
              </div>

              {/* Location */}
              <div>
                <p className="font-semibold mb-2 text-default-Secondary">
                  Location
                </p>
                <Select
                  placeholder="Any area"
                  selectedKeys={
                    selectedCity ? new Set([selectedCity]) : new Set()
                  }
                  onSelectionChange={handleCityChange}
                  variant="bordered"
                >
                  {cities.map(city => (
                    <SelectItem key={city}>
                      {city === '' ? 'Any area' : city}
                    </SelectItem>
                  ))}
                </Select>
              </div>

              {/* Price Range */}
              <div>
                <p className="font-semibold mb-2 text-default-Secondary">
                  Price range
                </p>
                <Slider
                  label=""
                  minValue={0}
                  maxValue={1000000}
                  step={1000}
                  value={price}
                  onChange={handlePriceChange}
                  formatOptions={{ style: 'currency', currency: 'USD' }}
                  aria-label="Price range slider"
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>${price[0].toLocaleString()}</span>
                  <span>${price[1].toLocaleString()}</span>
                </div>
              </div>

              {/* Amenities */}
              <div>
                <p className="font-semibold mb-2">Amenities</p>
                <div className="flex flex-wrap gap-2">
                  {amenitiesList.map(amenity => (
                    <Chip
                      key={amenity}
                      aria-labelledby={`Toggle ${amenity}`}
                      variant={
                        amenities.includes(amenity) ? 'solid' : 'bordered'
                      }
                      color={
                        amenities.includes(amenity) ? 'primary' : 'default'
                      }
                      onClick={() => toggleAmenity(amenity)}
                    >
                      {amenity}
                    </Chip>
                  ))}
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-between">
              <Button
                variant="light"
                color="primary"
                onPress={resetFilterData}
                className="font-semibold"
              >
                Reset all
              </Button>
              <Button
                onPress={applyFilterData}
                color="primary"
                className="font-semibold"
              >
                Apply
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
